import '../database/database_helper.dart';
import '../database/tables.dart';
import '../models/focus_session.dart';
import '../utils/constants.dart';

// 专注会话数据访问对象
class FocusSessionDao {
  final DatabaseHelper _dbHelper = DatabaseHelper.instance;

  // 插入专注会话
  Future<void> insertSession(FocusSession session) async {
    await _dbHelper.transaction((txn) async {
      final now = DateTime.now().millisecondsSinceEpoch;

      // 插入会话记录
      await txn.insert(DatabaseTables.focusSessions, {
        'id': session.id,
        'start_time': session.startTime.millisecondsSinceEpoch,
        'end_time': session.endTime?.millisecondsSinceEpoch,
        'duration_minutes': session.durationMinutes,
        'state': session.state.index,
        'elapsed_minutes': session.elapsedMinutes,
        'elapsed_seconds': session.elapsedSeconds,
        'is_completed': session.isCompleted ? 1 : 0,
        'created_at': now,
        'updated_at': now,
      });

      // 插入休息记录
      for (final breakRecord in session.breaks) {
        await txn.insert(DatabaseTables.breakRecords, {
          'session_id': session.id,
          'start_time': breakRecord.startTime.millisecondsSinceEpoch,
          'duration_seconds': breakRecord.durationSeconds,
          'reaction_time_seconds': breakRecord.reactionTimeSeconds,
          'created_at': now,
        });
      }
    });
  }

  // 更新专注会话
  Future<void> updateSession(FocusSession session) async {
    await _dbHelper.transaction((txn) async {
      final now = DateTime.now().millisecondsSinceEpoch;

      // 更新会话记录
      await txn.update(
        DatabaseTables.focusSessions,
        {
          'end_time': session.endTime?.millisecondsSinceEpoch,
          'state': session.state.index,
          'elapsed_minutes': session.elapsedMinutes,
          'elapsed_seconds': session.elapsedSeconds,
          'is_completed': session.isCompleted ? 1 : 0,
          'updated_at': now,
        },
        where: 'id = ?',
        whereArgs: [session.id],
      );

      // 删除旧的休息记录
      await txn.delete(
        DatabaseTables.breakRecords,
        where: 'session_id = ?',
        whereArgs: [session.id],
      );

      // 插入新的休息记录
      for (final breakRecord in session.breaks) {
        await txn.insert(DatabaseTables.breakRecords, {
          'session_id': session.id,
          'start_time': breakRecord.startTime.millisecondsSinceEpoch,
          'duration_seconds': breakRecord.durationSeconds,
          'reaction_time_seconds': breakRecord.reactionTimeSeconds,
          'created_at': now,
        });
      }
    });
  }

  // 根据ID获取专注会话
  Future<FocusSession?> getSessionById(String id) async {
    final sessions = await _dbHelper.query(
      DatabaseTables.focusSessions,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (sessions.isEmpty) return null;

    final sessionData = sessions.first;
    final breaks = await _getBreakRecords(id);

    return _mapToFocusSession(sessionData, breaks);
  }

  // 获取所有专注会话
  Future<List<FocusSession>> getAllSessions({
    int? limit,
    int? offset,
    String? orderBy,
  }) async {
    final sessions = await _dbHelper.query(
      DatabaseTables.focusSessions,
      orderBy: orderBy ?? 'start_time DESC',
      limit: limit,
      offset: offset,
    );

    final result = <FocusSession>[];
    for (final sessionData in sessions) {
      final breaks = await _getBreakRecords(sessionData['id'] as String);
      result.add(_mapToFocusSession(sessionData, breaks));
    }

    return result;
  }

  // 获取指定日期范围的会话
  Future<List<FocusSession>> getSessionsByDateRange(
    DateTime startDate,
    DateTime endDate, {
    bool? completedOnly,
  }) async {
    String where = 'start_time >= ? AND start_time <= ?';
    List<dynamic> whereArgs = [
      startDate.millisecondsSinceEpoch,
      endDate.millisecondsSinceEpoch,
    ];

    if (completedOnly == true) {
      where += ' AND is_completed = 1';
    }

    final sessions = await _dbHelper.query(
      DatabaseTables.focusSessions,
      where: where,
      whereArgs: whereArgs,
      orderBy: 'start_time DESC',
    );

    final result = <FocusSession>[];
    for (final sessionData in sessions) {
      final breaks = await _getBreakRecords(sessionData['id'] as String);
      result.add(_mapToFocusSession(sessionData, breaks));
    }

    return result;
  }

  // 获取今日会话
  Future<List<FocusSession>> getTodaySessions() async {
    final now = DateTime.now();
    final startOfDay = DateTime(now.year, now.month, now.day);
    final endOfDay = startOfDay.add(const Duration(days: 1));

    return await getSessionsByDateRange(startOfDay, endOfDay);
  }

  // 获取本周会话
  Future<List<FocusSession>> getWeekSessions() async {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final startOfWeekDay = DateTime(startOfWeek.year, startOfWeek.month, startOfWeek.day);
    final endOfWeek = startOfWeekDay.add(const Duration(days: 7));

    return await getSessionsByDateRange(startOfWeekDay, endOfWeek);
  }

  // 获取本月会话
  Future<List<FocusSession>> getMonthSessions() async {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 1);

    return await getSessionsByDateRange(startOfMonth, endOfMonth);
  }

  // 删除专注会话
  Future<void> deleteSession(String id) async {
    await _dbHelper.transaction((txn) async {
      // 删除休息记录（外键约束会自动删除）
      await txn.delete(
        DatabaseTables.breakRecords,
        where: 'session_id = ?',
        whereArgs: [id],
      );

      // 删除会话记录
      await txn.delete(
        DatabaseTables.focusSessions,
        where: 'id = ?',
        whereArgs: [id],
      );
    });
  }

  // 获取会话的休息记录
  Future<List<BreakRecord>> _getBreakRecords(String sessionId) async {
    final breaks = await _dbHelper.query(
      DatabaseTables.breakRecords,
      where: 'session_id = ?',
      whereArgs: [sessionId],
      orderBy: 'start_time ASC',
    );

    return breaks.map((breakData) => BreakRecord(
      startTime: DateTime.fromMillisecondsSinceEpoch(breakData['start_time'] as int),
      durationSeconds: breakData['duration_seconds'] as int,
      reactionTimeSeconds: (breakData['reaction_time_seconds'] as num).toDouble(),
    )).toList();
  }

  // 将数据库记录映射为FocusSession对象
  FocusSession _mapToFocusSession(
    Map<String, dynamic> sessionData,
    List<BreakRecord> breaks,
  ) {
    return FocusSession(
      id: sessionData['id'] as String,
      startTime: DateTime.fromMillisecondsSinceEpoch(sessionData['start_time'] as int),
      endTime: sessionData['end_time'] != null
          ? DateTime.fromMillisecondsSinceEpoch(sessionData['end_time'] as int)
          : null,
      durationMinutes: sessionData['duration_minutes'] as int,
      state: FocusSessionState.values[sessionData['state'] as int],
      elapsedMinutes: sessionData['elapsed_minutes'] as int,
      elapsedSeconds: sessionData['elapsed_seconds'] as int,
      breaks: breaks,
      isCompleted: (sessionData['is_completed'] as int) == 1,
    );
  }

  // 获取统计数据
  Future<Map<String, dynamic>> getStatistics() async {
    final totalSessions = await _dbHelper.rawQuery(
      'SELECT COUNT(*) as count FROM ${DatabaseTables.focusSessions}'
    );

    final completedSessions = await _dbHelper.rawQuery(
      'SELECT COUNT(*) as count FROM ${DatabaseTables.focusSessions} WHERE is_completed = 1'
    );

    final totalBreaks = await _dbHelper.rawQuery(
      'SELECT COUNT(*) as count FROM ${DatabaseTables.breakRecords}'
    );

    return {
      'total_sessions': totalSessions.first['count'],
      'completed_sessions': completedSessions.first['count'],
      'total_breaks': totalBreaks.first['count'],
    };
  }
}
